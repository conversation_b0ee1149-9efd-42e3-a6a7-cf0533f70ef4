using UnityEngine;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// Manages module pieces on the map to visually represent modules at each world location and orbit location.
/// Unlike ships, only shows 1 module object per world per player regardless of how many modules they have there.
/// Modules are positioned in a row under the ships.
/// </summary>
public class ModuleMapTracker : MonoBehaviour
{
    [Header("Module Prefabs")]
    [SerializeField] private GameObject redModulePrefab;
    [SerializeField] private GameObject greenModulePrefab;
    [SerializeField] private GameObject blueModulePrefab;
    [SerializeField] private GameObject yellowModulePrefab;
    
    [Header("Settings")]
    [SerializeField] private bool trackOrbitLocations = true;
    [SerializeField] private float moduleRowOffset = 1.5f; // How far below ships to place modules
    
    // Singleton pattern
    public static ModuleMapTracker Instance { get; private set; }
    
    // Track module pieces for each world and player (key is world/orbit location)
    // Only one module piece per player per world
    private Dictionary<GameObject, Dictionary<int, GameObject>> worldModulePieces = new Dictionary<GameObject, Dictionary<int, GameObject>>();
    
    // Player color mapping
    private GameObject[] playerModulePrefabs = new GameObject[4];
    
    private void Awake()
    {
        // Setup singleton
        if (Instance == null)
        {
            Instance = this;
        }
        else
        {
            Debug.LogWarning("Multiple ModuleMapTracker instances detected!");
            Destroy(gameObject);
            return;
        }
        
        // Initialize player module prefabs array
        playerModulePrefabs[0] = redModulePrefab;
        playerModulePrefabs[1] = greenModulePrefab;
        playerModulePrefabs[2] = blueModulePrefab;
        playerModulePrefabs[3] = yellowModulePrefab;
    }
    
    private void Start()
    {
        // Initialize module tracking for all worlds and orbit locations
        InitializeWorldModuleTracking();

        // Update module pieces for all players
        RefreshAllModulePieces();
    }

    /// <summary>
    /// Initialize the module prefabs (called by initializer script)
    /// </summary>
    public void InitializeModulePrefabs(GameObject red, GameObject green, GameObject blue, GameObject yellow)
    {
        redModulePrefab = red;
        greenModulePrefab = green;
        blueModulePrefab = blue;
        yellowModulePrefab = yellow;

        // Update the player module prefabs array
        playerModulePrefabs[0] = redModulePrefab;
        playerModulePrefabs[1] = greenModulePrefab;
        playerModulePrefabs[2] = blueModulePrefab;
        playerModulePrefabs[3] = yellowModulePrefab;
    }
    
    /// <summary>
    /// Initialize module tracking dictionaries for all worlds and orbit locations
    /// </summary>
    private void InitializeWorldModuleTracking()
    {
        WorldManager worldManager = WorldManager.Instance;
        if (worldManager == null)
        {
            Debug.LogError("WorldManager not found! Cannot initialize module tracking.");
            return;
        }

        // Get all celestial bodies (planets/moons with PlanetBody components)
        PlanetBody[] allBodies = FindObjectsByType<PlanetBody>(FindObjectsSortMode.None);

        foreach (PlanetBody body in allBodies)
        {
            GameObject worldObject = body.gameObject;

            // Skip orbit locations if tracking is disabled
            if (!trackOrbitLocations && IsOrbitLocation(worldObject.name))
                continue;

            // Initialize tracking for this world
            worldModulePieces[worldObject] = new Dictionary<int, GameObject>();
        }

        // Also get orbit locations from WorldManager (they don't have PlanetBody components)
        if (trackOrbitLocations)
        {
            List<GameObject> orbitLocations = worldManager.GetAllOrbitLocations();
            foreach (GameObject orbitLocation in orbitLocations)
            {
                if (orbitLocation == null) continue;

                // Initialize tracking for this orbit location
                worldModulePieces[orbitLocation] = new Dictionary<int, GameObject>();
            }
        }
    }
    
    /// <summary>
    /// Check if a world name indicates it's an orbit location
    /// </summary>
    private bool IsOrbitLocation(string worldName)
    {
        // Look for the specific Low[Planet]Orbit pattern
        return worldName.StartsWith("Low") && worldName.EndsWith("Orbit");
    }
    
    /// <summary>
    /// Refresh module pieces for all players and worlds
    /// </summary>
    public void RefreshAllModulePieces()
    {
        GameManager gameManager = GameManager.Instance;
        if (gameManager == null) return;
        
        // Clear all existing module pieces
        ClearAllModulePieces();
        
        // Update for each player
        foreach (Player player in gameManager.Players)
        {
            RefreshPlayerModulePieces(player.PlayerId);
        }
    }
    
    /// <summary>
    /// Refresh module pieces for a specific player
    /// </summary>
    public void RefreshPlayerModulePieces(int playerId)
    {
        GameManager gameManager = GameManager.Instance;
        if (gameManager == null || playerId >= gameManager.PlayerCount) return;
        
        Player player = gameManager.Players[playerId];
        if (player == null) return;
        
        // Clear existing pieces for this player
        ClearPlayerModulePieces(playerId);
        
        // Get all locations where this player has modules
        List<GameObject> locationsWithModules = player.GetPlanetBodiesWithModules();
        
        foreach (GameObject location in locationsWithModules)
        {
            if (location == null) continue;

            // Skip orbit locations if tracking is disabled
            if (!trackOrbitLocations && IsOrbitLocation(location.name))
                continue;

            // Skip if we're not tracking this world
            if (!worldModulePieces.ContainsKey(location))
                continue;

            // Get modules at this location
            List<Module> modulesAtLocation = player.GetModulesOnPlanet(location);
            
            // Only create a module piece if there are modules at this location
            if (modulesAtLocation.Count > 0)
            {
                CreateModulePieceAtWorldForPlayer(location, playerId);
            }
        }
    }

    /// <summary>
    /// Create a single module piece at a specific world for a player
    /// </summary>
    private void CreateModulePieceAtWorldForPlayer(GameObject world, int playerId)
    {
        GameObject modulePrefab = GetModulePrefabForPlayer(playerId);
        if (modulePrefab == null)
        {
            Debug.LogWarning($"No module prefab found for player {playerId}");
            return;
        }

        Vector3 worldPosition = world.transform.position;
        bool isOrbit = IsOrbitLocation(world.name);

        // Calculate module position - modules go in a row under the ships
        Vector3 modulePosition = CalculateModulePosition(worldPosition, playerId, isOrbit);

        GameObject modulePiece = Instantiate(modulePrefab, modulePosition, Quaternion.identity);
        modulePiece.name = $"ModulePiece_Player{playerId}_{(isOrbit ? "Orbit" : "World")}{world.name}";
        modulePiece.transform.SetParent(transform); // Parent to this manager

        // Add to tracking
        worldModulePieces[world][playerId] = modulePiece;
    }

    /// <summary>
    /// Calculate the position for a module piece based on world position and player ID
    /// For major planet orbits, modules are positioned vertically to the left of ships
    /// For other locations, modules are positioned in a row under the ships
    /// </summary>
    private Vector3 CalculateModulePosition(Vector3 worldPosition, int playerId, bool isOrbit)
    {
        if (isOrbit && IsMajorPlanetOrbitByName(GetWorldNameAtPosition(worldPosition)))
        {
            // Major planet orbits: arrange modules top to bottom to the left of ships
            // Position modules to the left (+x) of the ship area
            float x = worldPosition.x + 1; // Position to the left of ships (ships start at x+0)
            float y = worldPosition.y + 1.5f; // Same Y as ships

            // Calculate z offset for vertical positioning (top to bottom)
            // Center modules around the orbit position similar to ships
            int totalPlayers = 4; // Maximum players
            float totalDepth = (totalPlayers - 1) * 1f; // 1 unit spacing between modules
            float startZ = -totalDepth / 2f;
            float z = worldPosition.z + startZ + (playerId * 1f);

            return new Vector3(x, y, z);
        }
        else
        {
            // Default positioning: horizontal row under the ships
            // Each player gets one slot, spaced 1 unit apart horizontally
            // Starting position is similar to ships but offset down by moduleRowOffset

            float x = worldPosition.x + (1.5f - playerId) + 1; // Player 0 at +1.5, Player 1 at +0.5, etc. + adjustment
            float y = worldPosition.y + 1; // No Y adjustment needed (0)
            float z;

            if (isOrbit)
            {
                // For orbit locations, place modules below the ship area
                z = worldPosition.z + moduleRowOffset + 1; // + adjustment
            }
            else
            {
                // For regular worlds, place modules below the ship area
                z = worldPosition.z + (2.5f + moduleRowOffset) + 1; // + adjustment
            }

            return new Vector3(x, y, z);
        }
    }

    /// <summary>
    /// Get the module prefab for a specific player
    /// </summary>
    private GameObject GetModulePrefabForPlayer(int playerId)
    {
        if (playerId >= 0 && playerId < playerModulePrefabs.Length)
        {
            return playerModulePrefabs[playerId];
        }

        return null;
    }

    /// <summary>
    /// Get the world name at a given position by finding the closest world object
    /// </summary>
    private string GetWorldNameAtPosition(Vector3 position)
    {
        // Find the world GameObject at this position
        foreach (var worldEntry in worldModulePieces)
        {
            GameObject world = worldEntry.Key;
            if (world != null)
            {
                // Check if this world's position matches the given position (with some tolerance)
                Vector3 worldPos = world.transform.position;
                if (Vector3.Distance(worldPos, position) < 0.1f)
                {
                    return world.name;
                }
            }
        }

        return ""; // Return empty string if not found
    }

    /// <summary>
    /// Check if an orbit location name corresponds to a major planet orbit
    /// </summary>
    private bool IsMajorPlanetOrbitByName(string orbitName)
    {
        // Major planet orbit locations that should use vertical positioning
        string[] majorPlanetOrbits = new string[]
        {
            "LowSolarOrbit",
            "LowMercuryOrbit",
            "LowVenusOrbit",
            "LowEarthOrbit",
            "LowMarsOrbit",
            "LowJupiterOrbit",
            "LowSaturnOrbit",
            "LowUranusOrbit",
            "LowNeptuneOrbit"
        };

        foreach (string majorOrbit in majorPlanetOrbits)
        {
            if (orbitName == majorOrbit)
            {
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// Clear all module pieces from the map
    /// </summary>
    private void ClearAllModulePieces()
    {
        foreach (var worldEntry in worldModulePieces)
        {
            foreach (var playerEntry in worldEntry.Value)
            {
                if (playerEntry.Value != null)
                    Destroy(playerEntry.Value);
            }
            worldEntry.Value.Clear();
        }
    }

    /// <summary>
    /// Clear module pieces for a specific player
    /// </summary>
    private void ClearPlayerModulePieces(int playerId)
    {
        foreach (var worldEntry in worldModulePieces)
        {
            if (worldEntry.Value.ContainsKey(playerId))
            {
                if (worldEntry.Value[playerId] != null)
                    Destroy(worldEntry.Value[playerId]);
                worldEntry.Value.Remove(playerId);
            }
        }
    }

    /// <summary>
    /// Update module pieces when a player acquires a module
    /// </summary>
    public void OnPlayerAcquiredModule(int playerId, GameObject world)
    {
        RefreshPlayerModulePieces(playerId);
    }

    /// <summary>
    /// Update module pieces when a player removes a module
    /// </summary>
    public void OnPlayerRemovedModule(int playerId, GameObject world)
    {
        RefreshPlayerModulePieces(playerId);
    }
}
